2025-07-30 10:01:43.144  INFO 35060 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 35060 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-07-30 10:01:43.155  INFO 35060 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-07-30 10:01:43.178  INFO 35060 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-30 10:01:43.179  INFO 35060 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-30 10:01:44.220  INFO 35060 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tom<PERSON> initialized with port(s): 8543 (http)
2025-07-30 10:01:44.225  INFO 35060 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-30 10:01:44.226  INFO 35060 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-30 10:01:44.278  INFO 35060 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-30 10:01:44.278  INFO 35060 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1099 ms
2025-07-30 10:01:44.683  WARN 35060 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-07-30 10:01:46.276  INFO 35060 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 10:01:46.331  INFO 35060 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-30 10:01:46.403  INFO 35060 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-30 10:01:46.609  INFO 35060 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-07-30 10:01:46.609  INFO 35060 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-07-30 10:01:46.619  INFO 35060 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-07-30 10:01:46.655  INFO 35060 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-07-30 10:01:46.780  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-07-30 10:01:46.783  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-07-30 10:01:46.791  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-07-30 10:01:46.792  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-07-30 10:01:46.797  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-07-30 10:01:46.800  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-07-30 10:01:46.811  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-07-30 10:01:46.812  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-07-30 10:01:46.813  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-07-30 10:01:46.813  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-07-30 10:01:46.814  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-07-30 10:01:46.815  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-07-30 10:01:46.816  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-07-30 10:01:46.817  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-07-30 10:01:46.818  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-07-30 10:01:46.819  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-07-30 10:01:46.820  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-07-30 10:01:46.821  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-07-30 10:01:46.823  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-07-30 10:01:46.823  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-07-30 10:01:46.825  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-07-30 10:01:46.827  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-07-30 10:01:46.828  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-07-30 10:01:46.831  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-07-30 10:01:46.832  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-07-30 10:01:46.834  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-07-30 10:01:46.837  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-07-30 10:01:46.838  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-07-30 10:01:46.839  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-07-30 10:01:46.840  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-07-30 10:01:46.842  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-07-30 10:01:46.842  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-07-30 10:01:46.843  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-07-30 10:01:46.845  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-07-30 10:01:46.846  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-07-30 10:01:46.848  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-07-30 10:01:46.851  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-07-30 10:01:46.852  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-07-30 10:01:46.853  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-07-30 10:01:46.855  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-07-30 10:01:46.858  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-07-30 10:01:46.858  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-07-30 10:01:46.858  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-07-30 10:01:46.859  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-07-30 10:01:46.863  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-07-30 10:01:46.865  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-07-30 10:01:46.865  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-07-30 10:01:46.866  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-07-30 10:01:46.866  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-07-30 10:01:46.867  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-07-30 10:01:46.867  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-07-30 10:01:46.867  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-07-30 10:01:46.868  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-07-30 10:01:46.869  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-07-30 10:01:46.870  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-07-30 10:01:46.877  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-07-30 10:01:46.878  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-07-30 10:01:46.878  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-07-30 10:01:46.879  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-07-30 10:01:46.880  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-07-30 10:01:46.883  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-07-30 10:01:46.883  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-07-30 10:01:46.890  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-07-30 10:01:46.891  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-07-30 10:01:46.892  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-07-30 10:01:46.892  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-07-30 10:01:46.893  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-07-30 10:01:46.893  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-07-30 10:01:46.893  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-07-30 10:01:46.894  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-07-30 10:01:46.896  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-07-30 10:01:46.896  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-07-30 10:01:46.897  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-07-30 10:01:46.899  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-07-30 10:01:46.899  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-07-30 10:01:46.900  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-07-30 10:01:46.901  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-07-30 10:01:46.902  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-07-30 10:01:46.904  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-07-30 10:01:46.906  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-07-30 10:01:46.907  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-07-30 10:01:46.907  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-07-30 10:01:46.908  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-07-30 10:01:46.908  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-07-30 10:01:46.910  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-07-30 10:01:46.911  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-07-30 10:01:46.912  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-07-30 10:01:46.913  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-07-30 10:01:46.913  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-07-30 10:01:46.913  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-07-30 10:01:46.913  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-07-30 10:01:46.914  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-07-30 10:01:46.914  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-07-30 10:01:46.915  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-07-30 10:01:46.915  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-07-30 10:01:46.915  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-07-30 10:01:46.915  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-07-30 10:01:46.916  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-07-30 10:01:46.916  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-07-30 10:01:46.917  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-07-30 10:01:46.917  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-07-30 10:01:46.917  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-07-30 10:01:46.920  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-07-30 10:01:46.921  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-07-30 10:01:46.922  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-07-30 10:01:46.923  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-07-30 10:01:46.924  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-07-30 10:01:46.924  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-07-30 10:01:46.926  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-07-30 10:01:46.927  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-07-30 10:01:46.927  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-07-30 10:01:46.928  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-07-30 10:01:46.929  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-07-30 10:01:46.929  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-07-30 10:01:46.930  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-07-30 10:01:46.933  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-07-30 10:01:46.934  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-07-30 10:01:46.935  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-07-30 10:01:46.936  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-07-30 10:01:46.937  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-07-30 10:01:46.938  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-07-30 10:01:46.941  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-07-30 10:01:46.944  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-07-30 10:01:46.958  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-07-30 10:01:46.963  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-07-30 10:01:46.964  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-07-30 10:01:46.964  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-07-30 10:01:46.965  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-07-30 10:01:46.965  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-07-30 10:01:46.966  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-07-30 10:01:46.966  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-07-30 10:01:46.970  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-07-30 10:01:46.972  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-07-30 10:01:46.973  INFO 35060 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-07-30 10:01:46.989  INFO 35060 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 4.005 seconds (JVM running for 4.497)
2025-07-30 10:01:47.205  INFO 35060 --- [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 10:01:47.205  INFO 35060 --- [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-30 10:01:47.206  INFO 35060 --- [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-30 10:01:47.234  INFO 35060 --- [RMI TCP Connection(1)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-07-30 10:02:44.923  INFO 35060 --- [http-nio-8543-exec-2] c.p.controller.WeChatAuthController      : 📥 接收到授权请求 - code: [0c3mLb1w...], encryptedData: [已提供], iv: [已提供], parkName: [四季上东]
2025-07-30 10:02:44.923  INFO 35060 --- [http-nio-8543-exec-2] com.parkingmanage.utils.WeChatUtils      : 🔐 开始获取微信登录信息，code: [0c3mLb1w...]
2025-07-30 10:02:45.590  INFO 35060 --- [http-nio-8543-exec-2] com.parkingmanage.utils.WeChatUtils      : 📱 微信API响应: {"session_key":"tmRKU7Tpb8lRR4ciNw0aBA==","openid":"oShkM5djd6av0SRoBMby-yxND6wk"}
2025-07-30 10:02:45.620  INFO 35060 --- [http-nio-8543-exec-2] com.parkingmanage.utils.WeChatUtils      : ✅ 成功获取微信登录信息 - openid: [oShkM5dj...], unionid: [null]
2025-07-30 10:02:45.620  INFO 35060 --- [http-nio-8543-exec-2] com.parkingmanage.utils.WeChatUtils      : ✅ 成功获取真实微信登录信息
2025-07-30 10:02:45.620  INFO 35060 --- [http-nio-8543-exec-2] com.parkingmanage.utils.WeChatUtils      : 🔓 开始解密微信手机号
2025-07-30 10:02:45.620  INFO 35060 --- [http-nio-8543-exec-2] com.parkingmanage.utils.WeChatUtils      : ✅ 手机号解密成功
2025-07-30 10:02:45.620  INFO 35060 --- [http-nio-8543-exec-2] com.parkingmanage.utils.WeChatUtils      : 📱 获取到真实手机号: 13593527970
2025-07-30 10:02:45.620  INFO 35060 --- [http-nio-8543-exec-2] c.p.controller.WeChatAuthController      : ✅ 成功解密用户手机号: [13593527970]
2025-07-30 10:02:45.621  INFO 35060 --- [http-nio-8543-exec-2] c.p.controller.WeChatAuthController      : 🔐 微信授权成功 - 手机号: [13593527970], openid: [oShkM5dj...], unionid: [null]
2025-07-30 10:02:45.621  INFO 35060 --- [http-nio-8543-exec-2] c.p.controller.WeChatAuthController      : 🔍 开始五层角色查询，手机号: [13593527970], openid: [oShkM5djd6av0SRoBMby-yxND6wk], 停车场: [四季上东]
2025-07-30 10:02:45.621  INFO 35060 --- [http-nio-8543-exec-2] c.p.controller.WeChatAuthController      : 🔍 第一层查询：Butler表（管家）- 只验证手机号
2025-07-30 10:02:45.629  INFO 35060 --- [http-nio-8543-exec-2] c.p.controller.WeChatAuthController      : 📊 查询到管家总数: 5
2025-07-30 10:02:45.630  INFO 35060 --- [http-nio-8543-exec-2] c.p.controller.WeChatAuthController      : ✅ 第一层查询成功：找到管家角色（手机号匹配）
2025-07-30 10:02:45.630  INFO 35060 --- [http-nio-8543-exec-2] c.p.controller.WeChatAuthController      : ✅ 最终返回给前端的响应: code=0, msg=授权成功, data={userInfo=Butler(id=12, usercode=001, username=四季管家, phone=13593527970, province=黑龙江省, city=哈尔滨市, district=香坊区, community=四季上东, createdate=2025-07-10T10:37:09, createman=null, status=待确认, auditdate=null, openid=oShkM5djd6av0SRoBMby-yxND6wk, confirmdate=null), role=manager, parkName=四季上东, phone=13593527970, openid=oShkM5djd6av0SRoBMby-yxND6wk, permissions=[appointment.query, appointment.audit, appointment.query.all, violation.manage, violation.view.all, audit.member, audit.appointment, owner.manage, manage.facility], source=butler_table, verification=phone_only, roleText=管家}
2025-07-30 10:02:45.751 ERROR 35060 --- [http-nio-8543-exec-3] c.p.c.exception.GlobalExceptionHandler   : 异常信息：

org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:253) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:442) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:383) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:125) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:67) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:498) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1261) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1043) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963) ~[spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.10.jar:5.3.10]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.10.jar:5.3.10]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:681) [tomcat-embed-core-9.0.53.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.10.jar:5.3.10]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764) [tomcat-embed-core-9.0.53.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53) [tomcat-embed-websocket-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96) [spring-boot-actuator-2.5.5.jar:2.5.5]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.10.jar:5.3.10]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119) [spring-web-5.3.10.jar:5.3.10]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:893) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1726) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61) [tomcat-embed-core-9.0.53.jar:9.0.53]
	at java.lang.Thread.run(Thread.java:748) [na:1.8.0_202]

2025-07-30 10:02:45.753  WARN 35060 --- [http-nio-8543-exec-3] .m.m.a.ExceptionHandlerExceptionResolver : Resolved [org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' not supported]
2025-07-30 10:16:27.395  INFO 35060 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-07-30 10:16:27.408  INFO 35060 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-07-30 11:09:58.081  INFO 21292 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 21292 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-07-30 11:09:58.082  INFO 21292 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-07-30 11:09:58.102  INFO 21292 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-30 11:09:58.102  INFO 21292 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-30 11:09:58.909  INFO 21292 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-07-30 11:09:58.913  INFO 21292 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-30 11:09:58.913  INFO 21292 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-30 11:09:58.956  INFO 21292 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-30 11:09:58.956  INFO 21292 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 854 ms
2025-07-30 11:09:59.252  WARN 21292 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-07-30 11:10:00.207  INFO 21292 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 11:10:00.229  INFO 21292 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-30 11:10:00.261  INFO 21292 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-30 11:10:00.398  INFO 21292 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-07-30 11:10:00.398  INFO 21292 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-07-30 11:10:00.404  INFO 21292 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-07-30 11:10:00.438  INFO 21292 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-07-30 11:10:00.554  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-07-30 11:10:00.557  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-07-30 11:10:00.565  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-07-30 11:10:00.566  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-07-30 11:10:00.570  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-07-30 11:10:00.574  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-07-30 11:10:00.584  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-07-30 11:10:00.585  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-07-30 11:10:00.586  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-07-30 11:10:00.586  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-07-30 11:10:00.587  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-07-30 11:10:00.588  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-07-30 11:10:00.589  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-07-30 11:10:00.590  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-07-30 11:10:00.590  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-07-30 11:10:00.591  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-07-30 11:10:00.592  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-07-30 11:10:00.593  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-07-30 11:10:00.594  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-07-30 11:10:00.595  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-07-30 11:10:00.597  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-07-30 11:10:00.599  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-07-30 11:10:00.599  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-07-30 11:10:00.603  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-07-30 11:10:00.603  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-07-30 11:10:00.605  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-07-30 11:10:00.607  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-07-30 11:10:00.608  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-07-30 11:10:00.609  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-07-30 11:10:00.610  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-07-30 11:10:00.611  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-07-30 11:10:00.612  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-07-30 11:10:00.612  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-07-30 11:10:00.614  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-07-30 11:10:00.615  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-07-30 11:10:00.616  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-07-30 11:10:00.619  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-07-30 11:10:00.620  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-07-30 11:10:00.621  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-07-30 11:10:00.623  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-07-30 11:10:00.625  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-07-30 11:10:00.625  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-07-30 11:10:00.626  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-07-30 11:10:00.626  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-07-30 11:10:00.629  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-07-30 11:10:00.631  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-07-30 11:10:00.631  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-07-30 11:10:00.632  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-07-30 11:10:00.632  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-07-30 11:10:00.633  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-07-30 11:10:00.633  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-07-30 11:10:00.633  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-07-30 11:10:00.634  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-07-30 11:10:00.635  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-07-30 11:10:00.636  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-07-30 11:10:00.642  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-07-30 11:10:00.643  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-07-30 11:10:00.644  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-07-30 11:10:00.645  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-07-30 11:10:00.645  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-07-30 11:10:00.647  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-07-30 11:10:00.648  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-07-30 11:10:00.655  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-07-30 11:10:00.656  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-07-30 11:10:00.657  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-07-30 11:10:00.657  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-07-30 11:10:00.657  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-07-30 11:10:00.658  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-07-30 11:10:00.658  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-07-30 11:10:00.659  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-07-30 11:10:00.661  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-07-30 11:10:00.661  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-07-30 11:10:00.661  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-07-30 11:10:00.663  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-07-30 11:10:00.663  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-07-30 11:10:00.664  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-07-30 11:10:00.666  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-07-30 11:10:00.667  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-07-30 11:10:00.669  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-07-30 11:10:00.672  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-07-30 11:10:00.672  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-07-30 11:10:00.673  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-07-30 11:10:00.674  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-07-30 11:10:00.674  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-07-30 11:10:00.675  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-07-30 11:10:00.676  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-07-30 11:10:00.678  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-07-30 11:10:00.678  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-07-30 11:10:00.678  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-07-30 11:10:00.679  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-07-30 11:10:00.679  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-07-30 11:10:00.679  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-07-30 11:10:00.680  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-07-30 11:10:00.680  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-07-30 11:10:00.681  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-07-30 11:10:00.681  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-07-30 11:10:00.681  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-07-30 11:10:00.682  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-07-30 11:10:00.682  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-07-30 11:10:00.682  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-07-30 11:10:00.683  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-07-30 11:10:00.683  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-07-30 11:10:00.686  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-07-30 11:10:00.687  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-07-30 11:10:00.688  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-07-30 11:10:00.691  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-07-30 11:10:00.692  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-07-30 11:10:00.692  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-07-30 11:10:00.694  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-07-30 11:10:00.695  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-07-30 11:10:00.696  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-07-30 11:10:00.697  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-07-30 11:10:00.698  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-07-30 11:10:00.698  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-07-30 11:10:00.699  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-07-30 11:10:00.702  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-07-30 11:10:00.703  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-07-30 11:10:00.703  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-07-30 11:10:00.705  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-07-30 11:10:00.706  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-07-30 11:10:00.707  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-07-30 11:10:00.711  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-07-30 11:10:00.714  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-07-30 11:10:00.728  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-07-30 11:10:00.733  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-07-30 11:10:00.734  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-07-30 11:10:00.735  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-07-30 11:10:00.736  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-07-30 11:10:00.736  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-07-30 11:10:00.736  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-07-30 11:10:00.737  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-07-30 11:10:00.741  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-07-30 11:10:00.743  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-07-30 11:10:00.744  INFO 21292 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-07-30 11:10:00.758  INFO 21292 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 2.845 seconds (JVM running for 3.345)
2025-07-30 11:10:01.481  INFO 21292 --- [RMI TCP Connection(2)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:10:01.481  INFO 21292 --- [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-30 11:10:01.483  INFO 21292 --- [RMI TCP Connection(2)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-07-30 11:10:01.495  INFO 21292 --- [RMI TCP Connection(1)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-07-30 11:11:11.625  WARN 21292 --- [http-nio-8543-exec-2] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 70057
2025-07-30 11:15:24.593  WARN 21292 --- [http-nio-8543-exec-5] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 247771
2025-07-30 11:17:17.184  WARN 21292 --- [http-nio-8543-exec-1] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 85712
2025-07-30 11:20:58.152  WARN 21292 --- [http-nio-8543-exec-10] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 211394
2025-07-30 11:25:03.697  WARN 21292 --- [http-nio-8543-exec-5] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 197283
2025-07-30 11:26:34.741  WARN 21292 --- [http-nio-8543-exec-8] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 90883
2025-07-30 11:39:11.229  INFO 21292 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-07-30 11:39:11.231  INFO 21292 --- [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-07-30 13:45:47.078  INFO 27996 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 27996 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-07-30 13:45:47.087  INFO 27996 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-07-30 13:45:47.109  INFO 27996 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-07-30 13:45:47.109  INFO 27996 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-07-30 13:45:47.952  INFO 27996 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-07-30 13:45:47.956  INFO 27996 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-30 13:45:47.956  INFO 27996 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-30 13:45:48.000  INFO 27996 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-07-30 13:45:48.000  INFO 27996 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 890 ms
2025-07-30 13:45:48.347  WARN 27996 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-07-30 13:45:49.311  INFO 27996 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 13:45:49.335  INFO 27996 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-30 13:45:49.370  INFO 27996 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-30 13:45:49.514  INFO 27996 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-07-30 13:45:49.515  INFO 27996 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-07-30 13:45:49.521  INFO 27996 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-07-30 13:45:49.548  INFO 27996 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-07-30 13:45:49.647  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-07-30 13:45:49.650  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-07-30 13:45:49.657  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-07-30 13:45:49.658  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-07-30 13:45:49.662  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-07-30 13:45:49.665  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-07-30 13:45:49.675  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-07-30 13:45:49.676  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-07-30 13:45:49.677  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-07-30 13:45:49.677  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-07-30 13:45:49.678  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-07-30 13:45:49.679  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-07-30 13:45:49.680  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-07-30 13:45:49.681  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-07-30 13:45:49.681  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-07-30 13:45:49.682  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-07-30 13:45:49.683  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-07-30 13:45:49.684  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-07-30 13:45:49.685  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-07-30 13:45:49.686  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-07-30 13:45:49.687  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-07-30 13:45:49.689  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-07-30 13:45:49.690  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-07-30 13:45:49.694  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-07-30 13:45:49.694  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-07-30 13:45:49.696  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-07-30 13:45:49.698  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-07-30 13:45:49.699  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-07-30 13:45:49.700  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-07-30 13:45:49.701  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-07-30 13:45:49.702  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-07-30 13:45:49.703  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-07-30 13:45:49.704  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-07-30 13:45:49.705  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-07-30 13:45:49.707  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-07-30 13:45:49.708  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-07-30 13:45:49.711  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-07-30 13:45:49.711  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-07-30 13:45:49.713  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-07-30 13:45:49.714  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-07-30 13:45:49.717  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-07-30 13:45:49.718  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-07-30 13:45:49.718  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-07-30 13:45:49.718  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-07-30 13:45:49.721  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-07-30 13:45:49.723  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-07-30 13:45:49.723  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-07-30 13:45:49.724  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-07-30 13:45:49.724  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-07-30 13:45:49.724  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-07-30 13:45:49.725  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-07-30 13:45:49.725  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-07-30 13:45:49.726  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-07-30 13:45:49.727  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-07-30 13:45:49.727  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-07-30 13:45:49.733  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-07-30 13:45:49.734  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-07-30 13:45:49.734  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-07-30 13:45:49.735  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-07-30 13:45:49.736  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-07-30 13:45:49.738  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-07-30 13:45:49.739  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-07-30 13:45:49.745  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-07-30 13:45:49.745  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-07-30 13:45:49.746  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-07-30 13:45:49.746  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-07-30 13:45:49.747  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-07-30 13:45:49.747  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-07-30 13:45:49.747  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-07-30 13:45:49.748  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-07-30 13:45:49.750  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-07-30 13:45:49.750  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-07-30 13:45:49.750  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-07-30 13:45:49.752  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-07-30 13:45:49.752  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-07-30 13:45:49.754  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-07-30 13:45:49.755  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-07-30 13:45:49.756  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-07-30 13:45:49.758  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-07-30 13:45:49.760  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-07-30 13:45:49.760  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-07-30 13:45:49.761  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-07-30 13:45:49.761  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-07-30 13:45:49.762  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-07-30 13:45:49.763  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-07-30 13:45:49.764  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-07-30 13:45:49.765  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-07-30 13:45:49.766  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-07-30 13:45:49.766  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-07-30 13:45:49.766  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-07-30 13:45:49.767  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-07-30 13:45:49.767  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-07-30 13:45:49.767  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-07-30 13:45:49.768  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-07-30 13:45:49.768  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-07-30 13:45:49.768  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-07-30 13:45:49.768  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-07-30 13:45:49.769  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-07-30 13:45:49.769  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-07-30 13:45:49.769  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-07-30 13:45:49.770  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-07-30 13:45:49.770  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-07-30 13:45:49.772  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-07-30 13:45:49.773  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-07-30 13:45:49.774  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-07-30 13:45:49.775  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-07-30 13:45:49.776  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-07-30 13:45:49.776  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-07-30 13:45:49.778  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-07-30 13:45:49.778  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-07-30 13:45:49.779  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-07-30 13:45:49.780  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-07-30 13:45:49.780  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-07-30 13:45:49.781  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-07-30 13:45:49.781  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-07-30 13:45:49.784  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-07-30 13:45:49.785  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-07-30 13:45:49.785  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-07-30 13:45:49.787  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-07-30 13:45:49.788  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-07-30 13:45:49.789  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-07-30 13:45:49.792  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-07-30 13:45:49.795  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-07-30 13:45:49.808  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-07-30 13:45:49.813  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-07-30 13:45:49.814  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-07-30 13:45:49.814  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-07-30 13:45:49.815  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-07-30 13:45:49.815  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-07-30 13:45:49.815  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-07-30 13:45:49.816  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-07-30 13:45:49.820  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-07-30 13:45:49.822  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-07-30 13:45:49.824  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-07-30 13:45:49.836  INFO 27996 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 2.927 seconds (JVM running for 3.406)
2025-07-30 13:45:50.485  INFO 27996 --- [RMI TCP Connection(1)-*************] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 13:45:50.485  INFO 27996 --- [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-07-30 13:45:50.486  INFO 27996 --- [RMI TCP Connection(1)-*************] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-07-30 13:45:50.502  INFO 27996 --- [RMI TCP Connection(2)-*************] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} inited
2025-07-30 13:53:06.284  WARN 27996 --- [http-nio-8543-exec-4] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 380826
2025-07-30 14:07:35.014  WARN 27996 --- [http-nio-8543-exec-8] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 748753
2025-07-30 14:16:23.528  WARN 27996 --- [http-nio-8543-exec-1] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 528357
2025-07-30 14:19:37.321  WARN 27996 --- [http-nio-8543-exec-1] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 86661
2025-07-30 14:21:57.934  WARN 27996 --- [http-nio-8543-exec-7] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 129111
2025-07-30 14:26:35.356  WARN 27996 --- [http-nio-8543-exec-10] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 277262
2025-07-30 14:28:17.998  WARN 27996 --- [http-nio-8543-exec-3] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 102503
2025-07-30 14:34:19.970  WARN 27996 --- [http-nio-8543-exec-6] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 361822
2025-07-30 14:36:52.033  WARN 27996 --- [http-nio-8543-exec-9] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 151920
2025-07-30 14:39:32.325  WARN 27996 --- [http-nio-8543-exec-9] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 76904
2025-07-30 15:02:13.216  WARN 27996 --- [http-nio-8543-exec-9] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 1290783
2025-07-30 15:07:02.528  WARN 27996 --- [http-nio-8543-exec-3] c.a.druid.pool.DruidAbstractDataSource   : discard long time none received connection. , jdbcUrl : *****************************************************************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 280015
2025-07-30 15:07:43.347  INFO 27996 --- [Thread-9] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closing ...
2025-07-30 15:07:43.348  INFO 27996 --- [Thread-9] com.alibaba.druid.pool.DruidDataSource   : {dataSource-1} closed
2025-07-30 15:07:50.072  INFO 27996 --- [restartedMain] c.p.ParkingManageApplication             : Starting ParkingManageApplication using Java 1.8.0_202 on LAPTOP-MJMJLK4P with PID 27996 (D:\PakingDemo\parking-demo\target\classes started by L in D:\PakingDemo\parking-demo)
2025-07-30 15:07:50.072  INFO 27996 --- [restartedMain] c.p.ParkingManageApplication             : No active profile set, falling back to default profiles: default
2025-07-30 15:07:50.453  INFO 27996 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8543 (http)
2025-07-30 15:07:50.454  INFO 27996 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-07-30 15:07:50.454  INFO 27996 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.53]
2025-07-30 15:07:50.470  INFO 27996 --- [restartedMain] o.a.c.c.C.[Tomcat-1].[localhost].[/]     : Initializing Spring embedded WebApplicationContext
2025-07-30 15:07:50.470  INFO 27996 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 396 ms
2025-07-30 15:07:50.616  WARN 27996 --- [restartedMain] c.b.m.core.metadata.TableInfoHelper      : Can not find table primary key in Class: "com.parkingmanage.entity.Book".
2025-07-30 15:07:51.325  INFO 27996 --- [restartedMain] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-30 15:07:51.334  INFO 27996 --- [restartedMain] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
2025-07-30 15:07:51.346  INFO 27996 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-07-30 15:07:51.406  INFO 27996 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8543 (http) with context path ''
2025-07-30 15:07:51.407  INFO 27996 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Context refreshed
2025-07-30 15:07:51.408  INFO 27996 --- [restartedMain] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
2025-07-30 15:07:51.420  INFO 27996 --- [restartedMain] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
2025-07-30 15:07:51.459  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_1
2025-07-30 15:07:51.461  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_1
2025-07-30 15:07:51.468  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_1
2025-07-30 15:07:51.468  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_1
2025-07-30 15:07:51.473  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_1
2025-07-30 15:07:51.474  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_2
2025-07-30 15:07:51.483  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_2
2025-07-30 15:07:51.484  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_3
2025-07-30 15:07:51.485  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_1
2025-07-30 15:07:51.485  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_1
2025-07-30 15:07:51.487  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_2
2025-07-30 15:07:51.488  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_3
2025-07-30 15:07:51.488  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_4
2025-07-30 15:07:51.489  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_2
2025-07-30 15:07:51.489  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_2
2025-07-30 15:07:51.490  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_3
2025-07-30 15:07:51.490  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_4
2025-07-30 15:07:51.491  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_5
2025-07-30 15:07:51.492  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_3
2025-07-30 15:07:51.492  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_3
2025-07-30 15:07:51.493  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_4
2025-07-30 15:07:51.495  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_5
2025-07-30 15:07:51.495  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_6
2025-07-30 15:07:51.499  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_2
2025-07-30 15:07:51.499  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_6
2025-07-30 15:07:51.501  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_2
2025-07-30 15:07:51.502  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_7
2025-07-30 15:07:51.505  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_4
2025-07-30 15:07:51.505  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_4
2025-07-30 15:07:51.506  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_5
2025-07-30 15:07:51.507  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_1
2025-07-30 15:07:51.507  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_3
2025-07-30 15:07:51.508  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_7
2025-07-30 15:07:51.509  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_1
2025-07-30 15:07:51.509  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_1
2025-07-30 15:07:51.511  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_6
2025-07-30 15:07:51.514  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: removeBlackListCarUsingGET_1
2025-07-30 15:07:51.515  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_8
2025-07-30 15:07:51.516  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_7
2025-07-30 15:07:51.517  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_8
2025-07-30 15:07:51.519  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_9
2025-07-30 15:07:51.519  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_1
2025-07-30 15:07:51.519  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_1
2025-07-30 15:07:51.520  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: listByPhoneUsingGET_1
2025-07-30 15:07:51.523  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_4
2025-07-30 15:07:51.525  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_3
2025-07-30 15:07:51.525  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_10
2025-07-30 15:07:51.528  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertallocationUsingPOST_1
2025-07-30 15:07:51.528  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_9
2025-07-30 15:07:51.528  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_11
2025-07-30 15:07:51.529  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByOpenidUsingGET_2
2025-07-30 15:07:51.529  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getByIdUsingGET_2
2025-07-30 15:07:51.529  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertOwnerInfoUsingPOST_2
2025-07-30 15:07:51.531  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_5
2025-07-30 15:07:51.531  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_10
2025-07-30 15:07:51.537  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_12
2025-07-30 15:07:51.537  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_1
2025-07-30 15:07:51.538  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportPurchaseManagementUsingGET_2
2025-07-30 15:07:51.539  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_5
2025-07-30 15:07:51.540  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_5
2025-07-30 15:07:51.542  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryPageUsingGET_6
2025-07-30 15:07:51.543  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_11
2025-07-30 15:07:51.549  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_13
2025-07-30 15:07:51.549  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_6
2025-07-30 15:07:51.550  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_8
2025-07-30 15:07:51.550  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_1
2025-07-30 15:07:51.550  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_2
2025-07-30 15:07:51.550  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_12
2025-07-30 15:07:51.551  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_14
2025-07-30 15:07:51.552  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_9
2025-07-30 15:07:51.553  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_13
2025-07-30 15:07:51.554  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_15
2025-07-30 15:07:51.555  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: exportAllocationUsingGET_1
2025-07-30 15:07:51.556  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_6
2025-07-30 15:07:51.556  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_7
2025-07-30 15:07:51.557  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_10
2025-07-30 15:07:51.559  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_3
2025-07-30 15:07:51.559  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_14
2025-07-30 15:07:51.563  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_11
2025-07-30 15:07:51.567  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_12
2025-07-30 15:07:51.568  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_16
2025-07-30 15:07:51.569  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_7
2025-07-30 15:07:51.569  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_8
2025-07-30 15:07:51.570  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_13
2025-07-30 15:07:51.572  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_1
2025-07-30 15:07:51.573  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_15
2025-07-30 15:07:51.573  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: allPageUsingGET_4
2025-07-30 15:07:51.574  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: buildingListUsingGET_1
2025-07-30 15:07:51.574  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: cityListUsingGET_1
2025-07-30 15:07:51.574  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: communityListUsingGET_1
2025-07-30 15:07:51.575  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_17
2025-07-30 15:07:51.575  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: distinctPageUsingGET_1
2025-07-30 15:07:51.576  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: districtListUsingGET_1
2025-07-30 15:07:51.576  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: floorListUsingGET_1
2025-07-30 15:07:51.576  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getAllCommunityUsingGET_1
2025-07-30 15:07:51.576  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getDistinctCommunityUsingGET_1
2025-07-30 15:07:51.576  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertCommunityUsingPOST_1
2025-07-30 15:07:51.577  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: managePageUsingGET_2
2025-07-30 15:07:51.577  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: provinceListUsingGET_1
2025-07-30 15:07:51.577  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: queryCommunityUsingGET_1
2025-07-30 15:07:51.578  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: unitsListUsingGET_1
2025-07-30 15:07:51.579  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_16
2025-07-30 15:07:51.582  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_18
2025-07-30 15:07:51.583  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_8
2025-07-30 15:07:51.583  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_9
2025-07-30 15:07:51.584  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_14
2025-07-30 15:07:51.584  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_1
2025-07-30 15:07:51.585  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_17
2025-07-30 15:07:51.586  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_19
2025-07-30 15:07:51.587  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_9
2025-07-30 15:07:51.587  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_10
2025-07-30 15:07:51.588  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_15
2025-07-30 15:07:51.588  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertRoleUsingPOST_2
2025-07-30 15:07:51.589  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_18
2025-07-30 15:07:51.589  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_10
2025-07-30 15:07:51.592  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_20
2025-07-30 15:07:51.593  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findAllUsingGET_11
2025-07-30 15:07:51.593  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_11
2025-07-30 15:07:51.594  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_21
2025-07-30 15:07:51.596  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_16
2025-07-30 15:07:51.597  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_19
2025-07-30 15:07:51.600  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: batchDeleteUsingPOST_1
2025-07-30 15:07:51.605  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_17
2025-07-30 15:07:51.619  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPOST_1
2025-07-30 15:07:51.624  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: myFindPageUsingGET_2
2025-07-30 15:07:51.625  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_22
2025-07-30 15:07:51.626  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findByIdUsingGET_12
2025-07-30 15:07:51.627  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_18
2025-07-30 15:07:51.627  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: getListUsingGET_2
2025-07-30 15:07:51.628  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: insertUsingPOST_4
2025-07-30 15:07:51.628  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_20
2025-07-30 15:07:51.632  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: deleteUsingDELETE_23
2025-07-30 15:07:51.633  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: findPageUsingGET_19
2025-07-30 15:07:51.634  INFO 27996 --- [restartedMain] .d.s.w.r.o.CachingOperationNameGenerator : Generating unique operation named: updateUsingPUT_21
2025-07-30 15:07:51.639  INFO 27996 --- [restartedMain] c.p.ParkingManageApplication             : Started ParkingManageApplication in 1.584 seconds (JVM running for 4925.209)
2025-07-30 15:07:51.641  INFO 27996 --- [restartedMain] .ConditionEvaluationDeltaLoggingListener : Condition evaluation unchanged
